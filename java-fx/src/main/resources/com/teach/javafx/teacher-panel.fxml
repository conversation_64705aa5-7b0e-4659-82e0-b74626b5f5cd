<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.shape.*?>

<!--
    教师管理界面 FXML 配置文件

    功能说明：
    - 教师信息的增删改查操作
    - 教师列表显示和搜索
    - 教师详细信息编辑
    - 照片上传和显示
    - 数据导入功能

    对应控制器：com.teach.javafx.controller.TeacherController
    版本：2.0
    更新日期：2024
-->

<BorderPane prefWidth="1200.0" prefHeight="800.0"
           xmlns="http://javafx.com/javafx/17.0.12"
           xmlns:fx="http://javafx.com/fxml/1"
           fx:controller="com.teach.javafx.controller.TeacherController"
           styleClass="main-container">
    <!-- 顶部工具栏 -->
    <top>
        <VBox styleClass="toolbar-container">
            <!-- 标题栏 -->
            <HBox alignment="CENTER_LEFT" styleClass="title-bar">
                <children>
                    <Label text="教师管理系统" styleClass="title-label">
                        <font>
                            <Font name="System Bold" size="18.0" />
                        </font>
                    </Label>
                    <Region HBox.hgrow="ALWAYS" />
                    <Label fx:id="statusLabel" text="就绪" styleClass="status-label" />
                </children>
                <padding>
                    <Insets bottom="10.0" left="15.0" right="15.0" top="10.0" />
                </padding>
            </HBox>

            <!-- 操作工具栏 -->
            <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="action-toolbar">
                <children>
                    <!-- 左侧操作按钮组 -->
                    <HBox alignment="CENTER_LEFT" spacing="8.0" styleClass="button-group">
                        <children>
                            <Button fx:id="addButton" mnemonicParsing="false" onAction="#onAddButtonClick"
                                   text="新增教师" styleClass="primary-button">
                                <graphic>
                                    <Label text="新增" styleClass="button-icon" />
                                </graphic>
                                <tooltip>
                                    <Tooltip text="添加新的教师信息" />
                                </tooltip>
                            </Button>

                            <Button fx:id="deleteButton" mnemonicParsing="false" onAction="#onDeleteButtonClick"
                                   text="删除教师" styleClass="danger-button">
                                <graphic>
                                    <Label text="删除" styleClass="button-icon" />
                                </graphic>
                                <tooltip>
                                    <Tooltip text="删除选中的教师信息" />
                                </tooltip>
                            </Button>

                            <Separator orientation="VERTICAL" />

                            <Button fx:id="importButton" mnemonicParsing="false" onAction="#onImportButtonClick"
                                   text="批量导入" styleClass="secondary-button">
                                <graphic>
                                    <Label text="导入" styleClass="button-icon" />
                                </graphic>
                                <tooltip>
                                    <Tooltip text="从Excel文件批量导入教师数据" />
                                </tooltip>
                            </Button>
                        </children>
                    </HBox>

                    <!-- 中间弹性空间 -->
                    <Region HBox.hgrow="ALWAYS" />

                    <!-- 右侧搜索区域 -->
                    <HBox alignment="CENTER_RIGHT" spacing="8.0" styleClass="search-group">
                        <children>
                            <Label text="搜索:" styleClass="search-label">
                                <font>
                                    <Font name="System Bold" size="12.0" />
                                </font>
                            </Label>
                            <TextField fx:id="numNameTextField" prefWidth="200.0"
                                      promptText="输入工号或姓名进行搜索..."
                                      styleClass="search-field">
                                <tooltip>
                                    <Tooltip text="输入教师工号或姓名，支持模糊搜索" />
                                </tooltip>
                            </TextField>
                            <Button fx:id="queryButton" mnemonicParsing="false" onAction="#onQueryButtonClick"
                                   text="搜索" styleClass="search-button">
                                <tooltip>
                                    <Tooltip text="执行搜索操作" />
                                </tooltip>
                            </Button>
                        </children>
                    </HBox>
                </children>
                <padding>
                    <Insets bottom="10.0" left="15.0" right="15.0" top="5.0" />
                </padding>
            </HBox>
        </VBox>
    </top>
    <!-- 主内容区域 -->
    <center>
        <SplitPane dividerPositions="0.65" styleClass="main-split-pane">
            <items>
                <!-- 左侧：教师列表区域 -->
                <VBox styleClass="table-container">
                    <!-- 表格标题栏 -->
                    <HBox alignment="CENTER_LEFT" styleClass="table-header">
                        <children>
                            <Label text="教师列表" styleClass="table-title">
                                <font>
                                    <Font name="System Bold" size="14.0" />
                                </font>
                            </Label>
                            <Region HBox.hgrow="ALWAYS" />
                            <Label fx:id="recordCountLabel" text="共 0 条记录" styleClass="record-count-label" />
                        </children>
                        <padding>
                            <Insets bottom="8.0" left="10.0" right="10.0" top="8.0" />
                        </padding>
                    </HBox>

                    <!-- 教师信息表格 -->
                    <TableView fx:id="dataTableView" styleClass="data-table" VBox.vgrow="ALWAYS">
                        <columns>
                            <TableColumn fx:id="numColumn" prefWidth="100.0" text="工号"
                                        styleClass="table-column" resizable="true" sortable="true" />

                            <TableColumn fx:id="nameColumn" prefWidth="80.0" text="姓名"
                                        styleClass="table-column" resizable="true" sortable="true" />

                            <TableColumn fx:id="deptColumn" prefWidth="120.0" text="院系"
                                        styleClass="table-column" resizable="true" sortable="true" />

                            <TableColumn fx:id="titleColumn" prefWidth="80.0" text="职称"
                                        styleClass="table-column" resizable="true" sortable="true" />

                            <TableColumn fx:id="degreeColumn" prefWidth="80.0" text="学位"
                                        styleClass="table-column" resizable="true" sortable="true" />

                            <TableColumn fx:id="genderColumn" prefWidth="60.0" text="性别"
                                        styleClass="table-column" resizable="true" sortable="true" />

                            <TableColumn fx:id="phoneColumn" prefWidth="120.0" text="电话"
                                        styleClass="table-column" resizable="true" sortable="true" />

                            <TableColumn fx:id="emailColumn" prefWidth="150.0" text="邮箱"
                                        styleClass="table-column" resizable="true" sortable="true" />
                        </columns>

                        <!-- 表格样式和行为设置 -->
                        <columnResizePolicy>
                            <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                        </columnResizePolicy>

                        <!-- 空数据占位符 -->
                        <placeholder>
                            <VBox alignment="CENTER" spacing="10.0">
                                <children>
                                    <Label text="暂无数据" styleClass="empty-icon">
                                        <font>
                                            <Font size="24.0" />
                                        </font>
                                    </Label>
                                    <Label text="暂无教师数据" styleClass="empty-message">
                                        <font>
                                            <Font size="14.0" />
                                        </font>
                                    </Label>
                                    <Label text="点击新增教师按钮添加第一个教师" styleClass="empty-hint">
                                        <font>
                                            <Font size="12.0" />
                                        </font>
                                    </Label>
                                </children>
                            </VBox>
                        </placeholder>
                    </TableView>
                </VBox>
                <!-- 右侧：教师信息编辑区域 -->
                <VBox styleClass="form-container" spacing="15.0">
                    <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                    </padding>

                    <!-- 表单标题栏 -->
                    <HBox alignment="CENTER_LEFT" styleClass="form-header">
                        <children>
                            <Label text="教师信息" styleClass="form-title">
                                <font>
                                    <Font name="System Bold" size="14.0" />
                                </font>
                            </Label>
                            <Region HBox.hgrow="ALWAYS" />
                            <Label fx:id="formModeLabel" text="查看模式" styleClass="form-mode-label" />
                        </children>
                    </HBox>

                    <!-- 照片区域 -->
                    <VBox alignment="CENTER" styleClass="photo-section" spacing="10.0">
                        <children>
                            <Label text="教师照片" styleClass="section-label">
                                <font>
                                    <Font name="System Bold" size="12.0" />
                                </font>
                            </Label>

                            <VBox alignment="CENTER" spacing="8.0" styleClass="photo-container">
                                <children>
                                    <!-- 照片显示区域 -->
                                    <StackPane prefHeight="150.0" prefWidth="120.0" styleClass="photo-frame">
                                        <children>
                                            <!-- 照片显示 -->
                                            <ImageView fx:id="photoImageView" fitHeight="150.0" fitWidth="120.0"
                                                      pickOnBounds="true" preserveRatio="true" styleClass="photo-image">
                                                <image>
                                                    <!-- 默认占位图片可以在这里设置 -->
                                                </image>
                                            </ImageView>
                                        </children>
                                    </StackPane>

                                    <!-- 照片上传按钮 -->
                                    <Button fx:id="photoButton" onAction="#onPhotoButtonClick"
                                           text="上传照片" styleClass="photo-upload-button" prefWidth="120.0">
                                        <tooltip>
                                            <Tooltip text="点击上传教师照片（支持JPG、PNG格式，最大5MB）" />
                                        </tooltip>
                                    </Button>
                                </children>
                            </VBox>
                        </children>
                    </VBox>

                    <!-- 基本信息表单 -->
                    <VBox styleClass="form-section" spacing="12.0">
                        <children>
                            <Label text="基本信息" styleClass="section-label">
                                <font>
                                    <Font name="System Bold" size="12.0" />
                                </font>
                            </Label>

                            <GridPane hgap="15.0" vgap="12.0" styleClass="form-grid">
                                <columnConstraints>
                                    <ColumnConstraints halignment="RIGHT" hgrow="NEVER"
                                                     maxWidth="100.0" minWidth="80.0" prefWidth="90.0" />
                                    <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" prefWidth="200.0" />
                                </columnConstraints>
                                <rowConstraints>
                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="NEVER" />
                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="NEVER" />
                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="NEVER" />
                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="NEVER" />
                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="NEVER" />
                                </rowConstraints>
                                <children>
                                    <!-- 工号 -->
                                    <Label text="工号*:" styleClass="field-label" GridPane.rowIndex="0">
                                        <tooltip>
                                            <Tooltip text="教师工号，必填项" />
                                        </tooltip>
                                    </Label>
                                    <TextField fx:id="numField" styleClass="form-field"
                                              promptText="请输入教师工号..."
                                              GridPane.columnIndex="1" GridPane.rowIndex="0">
                                        <tooltip>
                                            <Tooltip text="教师工号，只能包含字母和数字" />
                                        </tooltip>
                                    </TextField>

                                    <!-- 姓名 -->
                                    <Label text="姓名*:" styleClass="field-label" GridPane.rowIndex="1">
                                        <tooltip>
                                            <Tooltip text="教师姓名，必填项" />
                                        </tooltip>
                                    </Label>
                                    <TextField fx:id="nameField" styleClass="form-field"
                                              promptText="请输入教师姓名..."
                                              GridPane.columnIndex="1" GridPane.rowIndex="1">
                                        <tooltip>
                                            <Tooltip text="教师真实姓名" />
                                        </tooltip>
                                    </TextField>

                                    <!-- 院系 -->
                                    <Label text="院系:" styleClass="field-label" GridPane.rowIndex="2">
                                        <tooltip>
                                            <Tooltip text="所属院系或部门" />
                                        </tooltip>
                                    </Label>
                                    <TextField fx:id="deptField" styleClass="form-field"
                                              promptText="请输入所属院系..."
                                              GridPane.columnIndex="1" GridPane.rowIndex="2">
                                        <tooltip>
                                            <Tooltip text="教师所属的院系或部门" />
                                        </tooltip>
                                    </TextField>

                                    <!-- 职称 -->
                                    <Label text="职称:" styleClass="field-label" GridPane.rowIndex="3">
                                        <tooltip>
                                            <Tooltip text="教师职称" />
                                        </tooltip>
                                    </Label>
                                    <TextField fx:id="titleField" styleClass="form-field"
                                              promptText="请输入职称..."
                                              GridPane.columnIndex="1" GridPane.rowIndex="3">
                                        <tooltip>
                                            <Tooltip text="如：教授、副教授、讲师等" />
                                        </tooltip>
                                    </TextField>

                                    <!-- 学位 -->
                                    <Label text="学位:" styleClass="field-label" GridPane.rowIndex="4">
                                        <tooltip>
                                            <Tooltip text="最高学位" />
                                        </tooltip>
                                    </Label>
                                    <TextField fx:id="degreeField" styleClass="form-field"
                                              promptText="请输入学位..."
                                              GridPane.columnIndex="1" GridPane.rowIndex="4">
                                        <tooltip>
                                            <Tooltip text="如：博士、硕士、学士等" />
                                        </tooltip>
                                    </TextField>
                                </children>
                            </GridPane>
                        </children>
                    </VBox>

                    <!-- 详细信息表单 -->
                    <VBox styleClass="form-section" spacing="12.0">
                        <children>
                            <Label text="详细信息" styleClass="section-label">
                                <font>
                                    <Font name="System Bold" size="12.0" />
                                </font>
                            </Label>

                            <GridPane hgap="15.0" vgap="12.0" styleClass="form-grid">
                                <columnConstraints>
                                    <ColumnConstraints halignment="RIGHT" hgrow="NEVER"
                                                     maxWidth="100.0" minWidth="80.0" prefWidth="90.0" />
                                    <ColumnConstraints hgrow="ALWAYS" minWidth="150.0" prefWidth="200.0" />
                                </columnConstraints>
                                <rowConstraints>
                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="NEVER" />
                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="NEVER" />
                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="NEVER" />
                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="NEVER" />
                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="NEVER" />
                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="NEVER" />
                                </rowConstraints>

                                <children>
                                    <!-- 证件号码 -->
                                    <Label text="证件号码:" styleClass="field-label" GridPane.rowIndex="0">
                                        <tooltip>
                                            <Tooltip text="身份证号码" />
                                        </tooltip>
                                    </Label>
                                    <TextField fx:id="cardField" styleClass="form-field"
                                              promptText="请输入身份证号码..."
                                              GridPane.columnIndex="1" GridPane.rowIndex="0">
                                        <tooltip>
                                            <Tooltip text="18位身份证号码" />
                                        </tooltip>
                                    </TextField>

                                    <!-- 性别 -->
                                    <Label text="性别:" styleClass="field-label" GridPane.rowIndex="1">
                                        <tooltip>
                                            <Tooltip text="选择性别" />
                                        </tooltip>
                                    </Label>
                                    <ComboBox fx:id="genderComboBox" styleClass="form-field"
                                             promptText="请选择性别..."
                                             GridPane.columnIndex="1" GridPane.rowIndex="1">
                                        <tooltip>
                                            <Tooltip text="选择教师性别" />
                                        </tooltip>
                                    </ComboBox>

                                    <!-- 出生日期 -->
                                    <Label text="出生日期:" styleClass="field-label" GridPane.rowIndex="2">
                                        <tooltip>
                                            <Tooltip text="出生日期" />
                                        </tooltip>
                                    </Label>
                                    <DatePicker fx:id="birthdayPick" styleClass="form-field"
                                               promptText="请选择出生日期..."
                                               GridPane.columnIndex="1" GridPane.rowIndex="2">
                                        <tooltip>
                                            <Tooltip text="选择教师出生日期" />
                                        </tooltip>
                                    </DatePicker>

                                    <!-- 邮箱 -->
                                    <Label text="邮箱:" styleClass="field-label" GridPane.rowIndex="3">
                                        <tooltip>
                                            <Tooltip text="电子邮箱地址" />
                                        </tooltip>
                                    </Label>
                                    <TextField fx:id="emailField" styleClass="form-field"
                                              promptText="请输入邮箱地址..."
                                              GridPane.columnIndex="1" GridPane.rowIndex="3">
                                        <tooltip>
                                            <Tooltip text="有效的电子邮箱地址" />
                                        </tooltip>
                                    </TextField>

                                    <!-- 电话 -->
                                    <Label text="电话:" styleClass="field-label" GridPane.rowIndex="4">
                                        <tooltip>
                                            <Tooltip text="联系电话" />
                                        </tooltip>
                                    </Label>
                                    <TextField fx:id="phoneField" styleClass="form-field"
                                              promptText="请输入联系电话..."
                                              GridPane.columnIndex="1" GridPane.rowIndex="4">
                                        <tooltip>
                                            <Tooltip text="手机号码或固定电话" />
                                        </tooltip>
                                    </TextField>

                                    <!-- 地址 -->
                                    <Label text="地址:" styleClass="field-label" GridPane.rowIndex="5">
                                        <tooltip>
                                            <Tooltip text="联系地址" />
                                        </tooltip>
                                    </Label>
                                    <TextField fx:id="addressField" styleClass="form-field"
                                              promptText="请输入联系地址..."
                                              GridPane.columnIndex="1" GridPane.rowIndex="5">
                                        <tooltip>
                                            <Tooltip text="详细的联系地址" />
                                        </tooltip>
                                    </TextField>
                                </children>
                            </GridPane>
                        </children>
                    </VBox>

                    <!-- 操作按钮区域 -->
                    <HBox alignment="CENTER" spacing="15.0" styleClass="button-bar">
                        <children>
                            <Button fx:id="saveButton" onAction="#onSaveButtonClick"
                                   text="保存信息" styleClass="save-button">
                                <graphic>
                                    <Label text="保存" styleClass="button-icon" />
                                </graphic>
                                <tooltip>
                                    <Tooltip text="保存当前教师信息" />
                                </tooltip>
                            </Button>

                            <Button fx:id="clearButton" onAction="#onAddButtonClick"
                                   text="清空表单" styleClass="clear-button">
                                <graphic>
                                    <Label text="清空" styleClass="button-icon" />
                                </graphic>
                                <tooltip>
                                    <Tooltip text="清空表单，准备添加新教师" />
                                </tooltip>
                            </Button>
                        </children>
                        <VBox.margin>
                            <Insets top="10.0" />
                        </VBox.margin>
                    </HBox>
                </VBox>
            </items>
        </SplitPane>
    </center>
</BorderPane>
