<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.text.*?>

<!--
    教师管理界面 FXML 配置文件

    功能说明：
    - 教师信息的增删改查操作
    - 教师列表显示和搜索
    - 教师详细信息编辑
    - 照片上传和显示
    - 数据导入功能

    对应控制器：com.teach.javafx.controller.TeacherController
    版本：2.0
    更新日期：2024
-->

<BorderPane prefWidth="1200.0" prefHeight="800.0"
           xmlns="http://javafx.com/javafx/17.0.12"
           xmlns:fx="http://javafx.com/fxml/1"
           fx:controller="com.teach.javafx.controller.TeacherController"
           styleClass="main-container">
    <top>
        <HBox id="HBox" alignment="CENTER_LEFT" spacing="5.0">
            <children>
                <FlowPane prefHeight="40.0" prefWidth="200.0" BorderPane.alignment="CENTER">
                    <children>
                        <Button mnemonicParsing="false" onAction="#onAddButtonClick" text="添加">
                            <FlowPane.margin>
                                <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                            </FlowPane.margin>
                        </Button>
                        <Button mnemonicParsing="false" onAction="#onDeleteButtonClick" text="删除">
                            <FlowPane.margin>
                                <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                            </FlowPane.margin>
                        </Button>
                        <Button mnemonicParsing="false" onAction="#onImportButtonClick" text="导入">
                            <FlowPane.margin>
                                <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                            </FlowPane.margin>
                        </Button>
                    </children>
                </FlowPane>
                <Pane prefHeight="-1.0" prefWidth="-1.0" HBox.hgrow="ALWAYS" />
                <FlowPane alignment="TOP_RIGHT" prefHeight="40.0" prefWidth="400.0" BorderPane.alignment="CENTER">
                    <children>
                        <Label prefWidth="49.0" text="工号姓名">
                            <FlowPane.margin>
                                <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                            </FlowPane.margin>
                        </Label>
                        <TextField fx:id="numNameTextField" prefWidth="100.0">
                            <FlowPane.margin>
                                <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                            </FlowPane.margin></TextField>
                        <Button mnemonicParsing="false" onAction="#onQueryButtonClick" text="查询">
                            <FlowPane.margin>
                                <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                            </FlowPane.margin></Button>
                    </children>
                </FlowPane>
            </children>
            <padding>
                <Insets bottom="3.0" left="3.0" right="3.0" top="3.0" />
            </padding>
        </HBox>
    </top>
    <center>
        <SplitPane dividerPositions="0.7">
            <items>
                <TableView fx:id="dataTableView" prefHeight="596.0" prefWidth="478.0">
                    <columns>
                        <TableColumn fx:id="numColumn" prefWidth="100.0" text="工号" />
                        <TableColumn fx:id="nameColumn" prefWidth="70.0" text="姓名" />
                        <TableColumn fx:id="deptColumn" prefWidth="75.0" text="院系" />
                        <TableColumn fx:id="titleColumn" prefWidth="75.0" text="职称" />
                        <TableColumn fx:id="degreeColumn" prefWidth="75.0" text="学位" />
                        <TableColumn fx:id="cardColumn" prefWidth="125.0" text="证件号码" />
                        <TableColumn fx:id="genderColumn" prefWidth="55.0" text="性别" />
                        <TableColumn fx:id="birthdayColumn" prefWidth="75.0" text="出生日期" />
                        <TableColumn fx:id="emailColumn" prefWidth="125.0" text="邮箱" />
                        <TableColumn fx:id="phoneColumn" prefWidth="95.0" text="电话" />
                        <TableColumn fx:id="addressColumn" prefWidth="145.0" text="地址" />
                    </columns>
                </TableView>
                <VBox alignment="TOP_CENTER" spacing="30.0">
                    <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                    </padding>
                    <GridPane hgap="10.0" vgap="10.0">
                        <columnConstraints>
                            <ColumnConstraints halignment="RIGHT" hgrow="NEVER" maxWidth="120.0" minWidth="80.0" prefWidth="100.0" />
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="193.0" minWidth="10.0" prefWidth="193.0" />
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="100" minWidth="10.0" prefWidth="100" />
                        </columnConstraints>
                        <rowConstraints>
                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                        </rowConstraints>
                        <children>
                            <Label text="工号" GridPane.rowIndex="0" minWidth="60" maxWidth="Infinity" wrapText="false" ellipsisString="">
                                <GridPane.margin>
                                    <Insets right="10.0" />
                                </GridPane.margin>
                            </Label>
                            <TextField fx:id="numField" GridPane.columnIndex="1" GridPane.rowIndex="0">
                                <padding>
                                    <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                                </padding>
                            </TextField>
                            <Label text="姓名" GridPane.rowIndex="1" minWidth="60" maxWidth="Infinity" wrapText="false" ellipsisString="">
                                <GridPane.margin>
                                    <Insets right="10.0" />
                                </GridPane.margin>
                            </Label>
                            <TextField fx:id="nameField" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                <padding>
                                    <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                                </padding>
                            </TextField>
                            <Label text="院系" GridPane.rowIndex="2" minWidth="60" maxWidth="Infinity" wrapText="false" ellipsisString="">
                                <GridPane.margin>
                                    <Insets right="10.0" />
                                </GridPane.margin>
                            </Label>
                            <TextField fx:id="deptField" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                <padding>
                                    <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                                </padding>
                            </TextField>
                            <Label text="职称" GridPane.rowIndex="3" minWidth="60" maxWidth="Infinity" wrapText="false" ellipsisString="">
                                <GridPane.margin>
                                    <Insets right="10.0" />
                                </GridPane.margin>
                            </Label>
                            <TextField fx:id="titleField" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                <padding>
                                    <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                                </padding>
                            </TextField>
                            <Label text="学位" GridPane.rowIndex="4" minWidth="60" maxWidth="Infinity" wrapText="false" ellipsisString="">
                                <GridPane.margin>
                                    <Insets right="10.0" />
                                </GridPane.margin>
                            </Label>
                            <TextField fx:id="degreeField" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                <padding>
                                    <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                                </padding>
                            </TextField>
                            <Label text="证件号码" GridPane.rowIndex="5" minWidth="60" maxWidth="Infinity" wrapText="false" ellipsisString="">
                                <GridPane.margin>
                                    <Insets right="10.0" />
                                </GridPane.margin>
                            </Label>
                            <TextField fx:id="cardField" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                <padding>
                                    <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                                </padding>
                            </TextField>
                            <Label text="性别" GridPane.rowIndex="6" minWidth="60" maxWidth="Infinity" wrapText="false" ellipsisString="">
                                <GridPane.margin>
                                    <Insets right="10.0" />
                                </GridPane.margin>
                            </Label>
                            <ComboBox fx:id="genderComboBox" prefWidth="150.0" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                            <Label text="出生日期" GridPane.rowIndex="7" minWidth="60" maxWidth="Infinity" wrapText="false" ellipsisString="">
                                <GridPane.margin>
                                    <Insets right="10.0" />
                                </GridPane.margin>
                            </Label>
                            <DatePicker fx:id="birthdayPick" prefWidth="150.0" GridPane.columnIndex="1" GridPane.rowIndex="7" />
                            <Label text="邮箱" GridPane.rowIndex="8" minWidth="60" maxWidth="Infinity" wrapText="false" ellipsisString="">
                                <GridPane.margin>
                                    <Insets right="10.0" />
                                </GridPane.margin>
                            </Label>
                            <TextField fx:id="emailField" GridPane.columnIndex="1" GridPane.rowIndex="8">
                                <padding>
                                    <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                                </padding>
                            </TextField>
                            <Label text="电话" GridPane.rowIndex="9" minWidth="60" maxWidth="Infinity" wrapText="false" ellipsisString="">
                                <GridPane.margin>
                                    <Insets right="10.0" />
                                </GridPane.margin>
                            </Label>
                            <TextField fx:id="phoneField" GridPane.columnIndex="1" GridPane.rowIndex="9">
                                <padding>
                                    <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                                </padding>
                            </TextField>
                            <Label text="地址" GridPane.rowIndex="10" minWidth="60" maxWidth="Infinity" wrapText="false" ellipsisString="">
                                <GridPane.margin>
                                    <Insets right="10.0" />
                                </GridPane.margin>
                            </Label>
                            <TextField fx:id="addressField" GridPane.columnIndex="1" GridPane.rowIndex="10">
                                <padding>
                                    <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                                </padding>
                            </TextField>
                            <Button fx:id="photoButton" onAction="#onPhotoButtonClick" styleClass="border_black_1" GridPane.columnIndex="2" GridPane.rowIndex="0" GridPane.rowSpan="4">
                                <GridPane.margin>
                                    <Insets right="10.0" />
                                </GridPane.margin>
                            </Button>
                            <ImageView fx:id="photoImageView" fitHeight="120.0" fitWidth="90.0" pickOnBounds="true" preserveRatio="true" GridPane.columnIndex="2" GridPane.rowIndex="0" GridPane.rowSpan="4" />
                        </children>
                    </GridPane>
                    <FlowPane alignment="CENTER" columnHalignment="CENTER" prefHeight="36.0">
                        <Button onAction="#onSaveButtonClick" text="保存">
                            <FlowPane.margin>
                                <Insets right="5.0" top="5.0" />
                            </FlowPane.margin>
                        </Button>
                    </FlowPane>
                </VBox>
            </items>
        </SplitPane>
    </center>
</BorderPane>
