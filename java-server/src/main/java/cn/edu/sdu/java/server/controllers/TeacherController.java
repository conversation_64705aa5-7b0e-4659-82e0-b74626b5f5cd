package cn.edu.sdu.java.server.controllers;

import cn.edu.sdu.java.server.payload.request.DataRequest;
import cn.edu.sdu.java.server.payload.response.DataResponse;
import cn.edu.sdu.java.server.services.TeacherService;
import cn.edu.sdu.java.server.util.CommonMethod;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * TeacherController 教师管理控制器
 * 提供教师信息的增删改查API接口
 *
 * 主要功能：
 * - 教师列表查询
 * - 教师信息详情
 * - 教师信息新增/修改
 * - 教师信息删除
 * - 分页查询
 * - 当前登录教师信息获取
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/teacher")
public class TeacherController {

    private static final Logger logger = LoggerFactory.getLogger(TeacherController.class);

    private final TeacherService teacherService;

    public TeacherController(TeacherService teacherService) {
        this.teacherService = teacherService;
    }

    @PostMapping("/getTeacherList")
    @PreAuthorize("hasRole('ADMIN')")
    public DataResponse getTeacherList(@Valid @RequestBody DataRequest dataRequest)
    {
        return teacherService.getTeacherList(dataRequest);
    }

    @PostMapping("/teacherDelete")
    public DataResponse teacherDelete(@Valid @RequestBody DataRequest dataRequest) {
        return teacherService.teacherDelete(dataRequest);
    }

    @PostMapping("/getTeacherInfo")
    @PreAuthorize("hasRole('ADMIN')")
    public DataResponse getTeacherInfo(@Valid @RequestBody DataRequest dataRequest) {
        return teacherService.getTeacherInfo(dataRequest);
    }

    @PostMapping("/getTeacherPageData")
    @PreAuthorize(" hasRole('ADMIN')")
    public DataResponse getTeacherPageData(@Valid @RequestBody DataRequest dataRequest) {
        return teacherService.getTeacherPageData(dataRequest);
    }

    @PostMapping("/teacherEditSave")
    @PreAuthorize(" hasRole('ADMIN')")
    public DataResponse teacherEditSave(@Valid @RequestBody DataRequest dataRequest) {
        return teacherService.teacherEditSave(dataRequest);
    }

    /**
     * getCurrentTeacherInfo 获取当前登录教师的个人信息
     * 使用CommonMethod.getPersonId()获取当前登录用户的personId
     * @return 返回教师个人信息
     */
    @PostMapping("/getCurrentTeacherInfo")
    @PreAuthorize("hasRole('TEACHER')")
    public DataResponse getCurrentTeacherInfo(@Valid @RequestBody DataRequest dataRequest) {
        Integer personId = CommonMethod.getPersonId();
        if (personId == null) {
            return CommonMethod.getReturnMessageError("未登录或登录已过期");
        }

        DataRequest req = new DataRequest();
        req.add("personId", personId);
        return teacherService.getTeacherInfo(req);
    }
}
